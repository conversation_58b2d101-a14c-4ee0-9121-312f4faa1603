extends Node

# 最终测试脚本 - 验证扑克游戏系统

func _ready():
	print("🎮 扑克游戏系统最终测试")
	print("=" * 40)
	
	test_poker_card_system()
	test_game_logic_system()
	test_ui_components()
	
	print("=" * 40)
	print("✅ 所有测试完成！扑克游戏系统已准备就绪。")
	print("💡 现在可以运行主场景体验完整的底栏扑克游戏！")

func test_poker_card_system():
	print("\n📱 测试扑克牌系统...")
	
	# 测试基本扑克牌创建
	var ace_hearts = PokerCard.new(PokerCard.Suit.HEARTS, PokerCard.Rank.ACE)
	var king_spades = PokerCard.new(PokerCard.Suit.SPADES, PokerCard.Rank.KING)
	
	print("  创建红桃A: %s" % ace_hearts.get_display_name())
	print("  创建黑桃K: %s" % king_spades.get_display_name())
	print("  红桃A纹理: %s" % ace_hearts.get_texture_path())
	print("  牌背纹理: %s" % ace_hearts.get_back_texture_path())
	print("  红桃A是红色: %s" % ace_hearts.is_red())
	print("  黑桃K是黑色: %s" % king_spades.is_black())
	print("  A vs K 比较: %d" % ace_hearts.compare_to(king_spades))
	
	# 验证纹理文件存在
	var texture_exists = ResourceLoader.exists(ace_hearts.get_texture_path())
	var back_exists = ResourceLoader.exists(ace_hearts.get_back_texture_path())
	print("  ✅ 纹理文件存在: %s" % texture_exists)
	print("  ✅ 牌背文件存在: %s" % back_exists)

func test_game_logic_system():
	print("\n🎯 测试游戏逻辑系统...")
	
	var game = PokerGame.new()
	print("  初始游戏状态: %s" % game.get_current_state())
	
	# 连接信号
	game.game_started.connect(_on_test_game_started)
	game.card_dealt.connect(_on_test_card_dealt)
	game.turn_changed.connect(_on_test_turn_changed)
	game.game_ended.connect(_on_test_game_ended)
	
	print("  开始新游戏...")
	game.start_new_game()
	
	print("  游戏状态: %s" % game.get_current_state())
	print("  玩家手牌数: %d" % game.get_player_hand().size())
	print("  庄家手牌数: %d" % game.get_dealer_hand().size())
	print("  玩家分数: %d" % game.get_player_score())
	print("  庄家可见分数: %d" % game.get_dealer_visible_score())
	print("  可以要牌: %s" % game.can_player_hit())
	print("  可以停牌: %s" % game.can_player_stand())

func test_ui_components():
	print("\n🖼️ 测试UI组件...")
	
	# 测试扑克牌UI场景加载
	var card_ui_scene = load("res://Scene/Poker/PokerCardUI.tscn")
	if card_ui_scene:
		print("  ✅ PokerCardUI场景加载成功")
		var card_ui = card_ui_scene.instantiate()
		if card_ui:
			print("  ✅ PokerCardUI实例化成功")
			card_ui.queue_free()
	else:
		print("  ❌ PokerCardUI场景加载失败")
	
	# 测试游戏UI场景加载
	var game_ui_scene = load("res://Scene/Poker/PokerGameUI.tscn")
	if game_ui_scene:
		print("  ✅ PokerGameUI场景加载成功")
		var game_ui = game_ui_scene.instantiate()
		if game_ui:
			print("  ✅ PokerGameUI实例化成功")
			game_ui.queue_free()
	else:
		print("  ❌ PokerGameUI场景加载失败")
	
	# 测试主界面场景加载
	var main_page_scene = load("res://Scene/UI/main_page.tscn")
	if main_page_scene:
		print("  ✅ 主界面场景加载成功")
	else:
		print("  ❌ 主界面场景加载失败")

# 信号回调函数
func _on_test_game_started():
	print("  📢 游戏开始信号")

func _on_test_card_dealt(card: PokerCard, is_player: bool):
	var player = "玩家" if is_player else "庄家"
	var face = "正面" if card.is_face_up else "背面"
	print("  🃏 发牌: %s 获得 %s (%s)" % [player, card.get_display_name(), face])

func _on_test_turn_changed(is_player_turn: bool):
	var turn = "玩家" if is_player_turn else "庄家"
	print("  🔄 回合切换: %s的回合" % turn)

func _on_test_game_ended(winner: String, player_score: int, dealer_score: int):
	print("  🏆 游戏结束: %s获胜 (玩家:%d vs 庄家:%d)" % [winner, player_score, dealer_score])
