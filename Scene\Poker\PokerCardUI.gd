class_name PokerCardUI
extends TextureButton

signal card_clicked(card_ui: PokerCardUI)
signal card_hovered(card_ui: PokerCardUI)

var poker_card: PokerCard
var is_selected: bool = false
var is_dragging: bool = false

# 动画相关
var original_position: Vector2
var original_scale: Vector2 = Vector2.ONE
var hover_scale: Vector2 = Vector2(1.1, 1.1)
var selected_offset: Vector2 = Vector2(0, -5)

func _ready():
    # 连接信号
    pressed.connect(_on_pressed)
    mouse_entered.connect(_on_mouse_entered)
    mouse_exited.connect(_on_mouse_exited)

    original_position = position
    original_scale = scale

# 设置扑克牌数据
func set_poker_card(card: PokerCard):
    poker_card = card
    update_texture()

# 更新纹理显示
func update_texture():
    if not poker_card:
        return
    
    var texture_path: String
    if poker_card.is_face_up:
        texture_path = poker_card.get_texture_path()
    else:
        texture_path = poker_card.get_back_texture_path()
    
    var texture = load(texture_path) as Texture2D
    if texture:
        texture_normal = texture
    else:
        print("警告：无法加载扑克牌纹理：", texture_path)

# 翻转扑克牌
func flip_card():
    if not poker_card:
        return
    
    # 创建翻转动画
    var tween = create_tween()
    tween.set_parallel(true)
    
    # 先缩放到0（看起来像侧面）
    tween.tween_property(self, "scale:x", 0.0, 0.15)
    
    # 在动画中间更换纹理
    tween.tween_callback(_flip_texture).set_delay(0.15)
    
    # 然后恢复缩放
    tween.tween_property(self, "scale:x", original_scale.x, 0.15).set_delay(0.15)

func _flip_texture():
    poker_card.is_face_up = !poker_card.is_face_up
    update_texture()

# 设置选中状态
func set_selected(selected: bool):
    if is_selected == selected:
        return
    
    is_selected = selected
    
    var tween = create_tween()
    tween.set_parallel(true)
    
    if is_selected:
        # 选中时向上移动并稍微放大
        tween.tween_property(self, "position", original_position + selected_offset, 0.2)
        tween.tween_property(self, "scale", original_scale * 1.05, 0.2)
        tween.tween_property(self, "modulate", Color(1.2, 1.2, 1.0), 0.2)
    else:
        # 取消选中时恢复原位
        tween.tween_property(self, "position", original_position, 0.2)
        tween.tween_property(self, "scale", original_scale, 0.2)
        tween.tween_property(self, "modulate", Color.WHITE, 0.2)

# 播放发牌动画
func play_deal_animation(from_position: Vector2, delay: float = 0.0):
    # 设置初始位置和状态
    position = from_position
    scale = Vector2.ZERO
    modulate = Color.TRANSPARENT
    
    # 创建发牌动画
    var tween = create_tween()
    tween.set_parallel(true)
    
    # 延迟后开始动画
    tween.tween_property(self, "position", original_position, 0.5).set_delay(delay).set_trans(Tween.TRANS_BACK).set_ease(Tween.EASE_OUT)
    tween.tween_property(self, "scale", original_scale, 0.3).set_delay(delay).set_trans(Tween.TRANS_BACK).set_ease(Tween.EASE_OUT)
    tween.tween_property(self, "modulate", Color.WHITE, 0.2).set_delay(delay)

# 播放收牌动画
func play_collect_animation(to_position: Vector2, delay: float = 0.0):
    var tween = create_tween()
    tween.set_parallel(true)
    
    tween.tween_property(self, "position", to_position, 0.3).set_delay(delay).set_trans(Tween.TRANS_CUBIC).set_ease(Tween.EASE_IN)
    tween.tween_property(self, "scale", Vector2.ZERO, 0.3).set_delay(delay).set_trans(Tween.TRANS_CUBIC).set_ease(Tween.EASE_IN)
    tween.tween_property(self, "modulate", Color.TRANSPARENT, 0.3).set_delay(delay)
    
    # 动画结束后隐藏
    tween.tween_callback(hide).set_delay(delay + 0.3)

func _on_pressed():
    card_clicked.emit(self)

func _on_mouse_entered():
    if not is_selected:
        var tween = create_tween()
        tween.tween_property(self, "scale", hover_scale, 0.1)
    
    card_hovered.emit(self)

func _on_mouse_exited():
    if not is_selected:
        var tween = create_tween()
        tween.tween_property(self, "scale", original_scale, 0.1)

# 获取扑克牌信息
func get_card() -> PokerCard:
    return poker_card

# 检查是否可以点击
func is_clickable() -> bool:
    return poker_card != null and poker_card.is_face_up

# 设置卡牌大小
func set_card_size(new_size: Vector2):
    custom_minimum_size = new_size
    size = new_size
