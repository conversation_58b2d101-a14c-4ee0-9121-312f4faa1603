extends Node

# 测试优化后的扑克牌布局

func _ready():
	print("🎮 测试优化后的扑克牌布局")
	print("=" * 50)
	
	test_card_size_optimization()
	test_spacing_optimization()
	test_extreme_scenarios()
	
	print("=" * 50)
	print("✅ 布局优化测试完成！")

func test_card_size_optimization():
	print("\n📏 测试扑克牌尺寸优化：")
	
	var old_size = Vector2(24, 32)
	var new_size = Vector2(16, 22)
	
	print("  原始尺寸: %dx%d" % [old_size.x, old_size.y])
	print("  优化尺寸: %dx%d" % [new_size.x, new_size.y])
	
	var size_reduction = (1 - (new_size.x * new_size.y) / (old_size.x * old_size.y)) * 100
	print("  面积减少: %.1f%%" % size_reduction)
	
	# 检查宽高比
	var old_ratio = old_size.x / old_size.y
	var new_ratio = new_size.x / new_size.y
	print("  原始宽高比: %.3f" % old_ratio)
	print("  优化宽高比: %.3f" % new_ratio)
	
	var ratio_change = abs(new_ratio - old_ratio) / old_ratio * 100
	if ratio_change < 5:
		print("  ✅ 宽高比保持良好")
	else:
		print("  ⚠️ 宽高比变化较大: %.1f%%" % ratio_change)

func test_spacing_optimization():
	print("\n📐 测试间距优化：")
	
	var old_spacing = 2
	var new_spacing = 1
	
	print("  原始间距: %d像素" % old_spacing)
	print("  优化间距: %d像素" % new_spacing)
	
	# 测试不同数量的牌在不同屏幕宽度下的表现
	var screen_widths = [1920, 1366, 1024]
	var card_counts = [2, 4, 6, 8]
	
	for width in screen_widths:
		print("  屏幕宽度: %d像素" % width)
		var area_width = width / 3 - 20  # 减去边距
		
		for count in card_counts:
			var total_width = count * 16 + (count - 1) * new_spacing
			var fits = total_width <= area_width
			var status = "✅" if fits else "❌"
			print("    %d张牌: %d像素 %s" % [count, total_width, status])

func test_extreme_scenarios():
	print("\n🃏 测试极端场景：")
	
	# 模拟极端情况下的布局
	var scenarios = [
		{"name": "小屏幕多牌", "width": 800, "cards": 8},
		{"name": "超宽屏少牌", "width": 2560, "cards": 2},
		{"name": "标准屏标准牌", "width": 1920, "cards": 5}
	]
	
	for scenario in scenarios:
		print("  场景: %s" % scenario.name)
		var area_width = scenario.width / 3 - 20
		var card_count = scenario.cards
		var card_width = 16
		
		# 计算最优间距
		var available_width = area_width - card_count * card_width
		var spacing = available_width / (card_count - 1) if card_count > 1 else 0
		
		print("    区域宽度: %d像素" % area_width)
		print("    卡牌数量: %d张" % card_count)
		print("    计算间距: %.1f像素" % spacing)
		
		if spacing >= 1:
			print("    ✅ 正常显示")
		elif spacing >= -2:
			print("    ⚠️ 轻微重叠")
		else:
			print("    ❌ 严重重叠")
		
		# 给出建议
		if spacing < 0:
			var overlap = abs(spacing)
			print("    💡 建议: 每张牌重叠 %.1f像素" % overlap)
		print("")

# 计算在给定约束下的最优卡牌尺寸
func calculate_optimal_size(bottom_bar_height: int, max_cards: int, area_width: int) -> Vector2:
	# 为UI元素预留空间
	var available_height = bottom_bar_height * 0.6  # 60%给卡牌
	
	# 计算最大卡牌宽度
	var min_spacing = 1
	var available_width = area_width - 20  # 减去边距
	var max_card_width = (available_width - (max_cards - 1) * min_spacing) / max_cards
	
	# 保持扑克牌比例
	var standard_ratio = 16.0 / 22.0  # 基于我们的优化尺寸
	
	var card_height = available_height
	var card_width = card_height * standard_ratio
	
	# 如果宽度超限，以宽度为准
	if card_width > max_card_width:
		card_width = max_card_width
		card_height = card_width / standard_ratio
	
	return Vector2(card_width, card_height)
