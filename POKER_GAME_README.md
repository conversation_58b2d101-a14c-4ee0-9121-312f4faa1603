# 扑克游戏底栏界面

## 概述
已成功将主界面的底栏区域完全改造为扑克游戏界面，实现了一个完整的21点扑克游戏。

## 游戏特性

### 🎮 游戏规则
- **21点游戏**：玩家和庄家比较点数，目标是接近21点但不超过
- **牌面计分**：
  - A = 1 或 11（自动选择最优值）
  - J, Q, K = 10
  - 其他牌 = 面值
- **游戏流程**：发牌 → 玩家回合 → 庄家回合 → 结算

### 🎨 界面布局
底栏被分为三个区域：
- **左侧**：庄家手牌和分数显示
- **中间**：游戏状态、控制按钮（要牌、停牌、新游戏）
- **右侧**：玩家手牌和分数显示

### ✨ 视觉效果
- **发牌动画**：卡牌从中央飞向玩家/庄家区域
- **翻牌动画**：庄家背面牌翻转为正面
- **选中效果**：卡牌悬停和选中状态
- **pixel主题**：使用Assets/poker/pixel目录下的像素风格扑克牌

## 如何游戏

1. **开始游戏**：点击"新游戏"按钮
2. **玩家回合**：
   - 点击"要牌"获得新卡牌
   - 点击"停牌"结束回合
   - 分数超过21点自动爆牌
3. **庄家回合**：庄家自动要牌直到分数≥17
4. **结算**：比较双方分数，显示获胜者

## 技术实现

### 文件结构
```
Scene/
├── Poker/
│   ├── PokerCard.gd          # 扑克牌数据类
│   ├── PokerCardUI.gd        # 扑克牌UI组件
│   ├── PokerCardUI.tscn      # 扑克牌UI场景
│   ├── PokerGame.gd          # 游戏逻辑类
│   ├── PokerGameUI.gd        # 游戏主界面
│   └── PokerGameUI.tscn      # 游戏主界面场景
└── UI/
    ├── main_page.gd          # 简化的主界面脚本
    └── main_page.tscn        # 更新的主界面场景
```

### 核心组件
- **PokerCard**：扑克牌数据结构，包含花色、牌面、纹理路径等
- **PokerCardUI**：可交互的扑克牌UI组件，支持动画效果
- **PokerGame**：游戏逻辑管理，处理发牌、计分、胜负判定
- **PokerGameUI**：游戏主界面，协调UI和逻辑

### 适配优化
- **底栏适配**：所有UI元素针对50像素高度优化
- **响应式布局**：支持不同屏幕宽度的自适应
- **性能优化**：使用对象池和动画缓存

## 扩展功能

### 可添加的功能
- 多种扑克游戏（德州扑克、梭哈等）
- 主题切换（pixel、light、dark）
- 音效和背景音乐
- 统计和成就系统
- 多人游戏支持

### 主题切换
当前使用pixel主题，可通过修改PokerCard.gd中的theme变量切换：
```gdscript
var theme = "pixel"  # 可选: "pixel", "light", "dark"
```

## 故障排除

### 常见问题
1. **卡牌不显示**：检查Assets/poker/pixel目录是否存在
2. **动画卡顿**：降低动画复杂度或增加延迟
3. **布局错乱**：确保底栏高度设置为50像素

### 调试模式
可以启用test_poker.gd来测试游戏逻辑：
```gdscript
# 在主场景中添加测试节点
var test = preload("res://test_poker.gd").new()
add_child(test)
```

## 更新日志

### v1.0.0 (当前版本)
- ✅ 完全移除原有底栏UI元素
- ✅ 实现21点扑克游戏
- ✅ 添加发牌和翻牌动画
- ✅ 使用pixel主题扑克牌素材
- ✅ 适配50像素高度底栏布局
- ✅ 支持全屏宽度响应式设计
