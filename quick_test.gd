extends Node

# 快速测试当前状态

func _ready():
	print("🔍 快速测试扑克游戏状态")
	print("=" * 30)
	
	test_basic_loading()
	test_card_creation()
	test_game_logic()
	
	print("=" * 30)
	print("测试完成！")

func test_basic_loading():
	print("\n📁 测试文件加载...")
	
	# 测试场景文件
	var scenes = [
		"res://Scene/Poker/PokerCardUI.tscn",
		"res://Scene/Poker/PokerGameUI.tscn",
		"res://Scene/UI/main_page.tscn"
	]
	
	for scene_path in scenes:
		if ResourceLoader.exists(scene_path):
			var scene = load(scene_path)
			if scene:
				print("  ✅ %s 加载成功" % scene_path.get_file())
			else:
				print("  ❌ %s 加载失败" % scene_path.get_file())
		else:
			print("  ❌ %s 文件不存在" % scene_path.get_file())

func test_card_creation():
	print("\n🃏 测试扑克牌创建...")
	
	var card = PokerCard.new(PokerCard.Suit.HEARTS, PokerCard.Rank.ACE)
	if card:
		print("  ✅ 扑克牌创建成功: %s" % card.get_display_name())
		print("  ✅ 纹理路径: %s" % card.get_texture_path())
		
		# 检查纹理文件是否存在
		if ResourceLoader.exists(card.get_texture_path()):
			print("  ✅ 纹理文件存在")
		else:
			print("  ❌ 纹理文件不存在")
	else:
		print("  ❌ 扑克牌创建失败")

func test_game_logic():
	print("\n🎮 测试游戏逻辑...")
	
	var game = PokerGame.new()
	if game:
		print("  ✅ 游戏逻辑创建成功")
		print("  ✅ 初始状态: %s" % game.get_current_state())
	else:
		print("  ❌ 游戏逻辑创建失败")
	
	print("\n💡 如果所有测试都通过，说明基本功能正常")
	print("   如果有问题，请提供具体的错误信息")
