extends Node

# 简单的扑克游戏测试脚本

func _ready():
	print("开始测试扑克游戏...")
	test_poker_card()
	test_poker_game()
	print("扑克游戏测试完成！")

func test_poker_card():
	print("\n=== 测试扑克牌类 ===")
	
	# 创建一张红桃A
	var card = PokerCard.new(PokerCard.Suit.HEARTS, PokerCard.Rank.ACE)
	print("创建了一张牌：", card.get_display_name())
	print("纹理路径：", card.get_texture_path())
	print("是否为红色：", card.is_red())
	print("牌值：", card.get_value())
	
	# 创建一张黑桃K
	var king = PokerCard.new(PokerCard.Suit.SPADES, PokerCard.Rank.KING)
	print("创建了一张牌：", king.get_display_name())
	print("比较A和K：", card.compare_to(king))

func test_poker_game():
	print("\n=== 测试扑克游戏逻辑 ===")
	
	var game = PokerGame.new()
	print("游戏状态：", game.get_current_state())
	
	# 连接信号
	game.game_started.connect(_on_game_started)
	game.card_dealt.connect(_on_card_dealt)
	game.game_ended.connect(_on_game_ended)
	
	# 开始游戏
	game.start_new_game()
	
	print("玩家手牌数量：", game.get_player_hand().size())
	print("庄家手牌数量：", game.get_dealer_hand().size())
	print("玩家分数：", game.get_player_score())
	print("庄家可见分数：", game.get_dealer_visible_score())

func _on_game_started():
	print("游戏开始信号触发")

func _on_card_dealt(card: PokerCard, is_player: bool):
	var player_type = "玩家" if is_player else "庄家"
	print("发牌：%s 获得 %s" % [player_type, card.get_display_name()])

func _on_game_ended(winner: String, player_score: int, dealer_score: int):
	print("游戏结束：%s获胜，玩家：%d，庄家：%d" % [winner, player_score, dealer_score])
