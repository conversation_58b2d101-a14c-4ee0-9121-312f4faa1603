extends Node

# 测试扑克牌布局和尺寸优化

func _ready():
	print("🃏 测试扑克牌布局优化")
	print("=" * 40)
	
	test_card_dimensions()
	test_layout_spacing()
	test_multiple_cards_display()
	
	print("=" * 40)
	print("✅ 布局测试完成！")

func test_card_dimensions():
	print("\n📏 测试卡牌尺寸...")
	
	# 底栏高度为50像素，需要为卡牌留出合理空间
	var bottom_bar_height = 50
	var card_height = 22  # 新的卡牌高度
	var card_width = 16   # 新的卡牌宽度
	
	print("  底栏高度: %d像素" % bottom_bar_height)
	print("  卡牌尺寸: %dx%d像素" % [card_width, card_height])
	print("  高度占比: %.1f%%" % (card_height * 100.0 / bottom_bar_height))
	
	# 检查宽高比
	var aspect_ratio = card_width / float(card_height)
	var standard_ratio = 2.5 / 3.5  # 标准扑克牌比例
	print("  当前宽高比: %.2f" % aspect_ratio)
	print("  标准宽高比: %.2f" % standard_ratio)
	print("  比例差异: %.2f%%" % (abs(aspect_ratio - standard_ratio) * 100.0 / standard_ratio))

func test_layout_spacing():
	print("\n📐 测试布局间距...")
	
	# 计算可用空间
	var screen_width = 1920  # 假设屏幕宽度
	var main_separation = 3  # 主容器间距
	var card_separation = 1  # 卡牌间距
	
	# 三个区域的可用宽度（平均分配）
	var area_width = (screen_width - 2 * main_separation) / 3
	
	print("  屏幕宽度: %d像素" % screen_width)
	print("  每个区域宽度: %.0f像素" % area_width)
	print("  主容器间距: %d像素" % main_separation)
	print("  卡牌间距: %d像素" % card_separation)
	
	# 计算每个区域最多能显示多少张牌
	var card_width = 16
	var max_cards_per_area = int((area_width + card_separation) / (card_width + card_separation))
	print("  每区域最多卡牌数: %d张" % max_cards_per_area)

func test_multiple_cards_display():
	print("\n🃏 测试多张牌显示...")
	
	# 模拟不同数量的卡牌
	var test_scenarios = [
		{"name": "初始发牌", "cards": 2},
		{"name": "玩家要牌一次", "cards": 3},
		{"name": "玩家要牌两次", "cards": 4},
		{"name": "玩家要牌三次", "cards": 5},
		{"name": "极限情况", "cards": 8}
	]
	
	var card_width = 16
	var card_separation = 1
	var area_width = 640  # 每个区域约640像素
	
	for scenario in test_scenarios:
		var cards_count = scenario.cards
		var total_width = cards_count * card_width + (cards_count - 1) * card_separation
		var fits = total_width <= area_width
		var overflow = max(0, total_width - area_width)
		
		print("  %s (%d张):" % [scenario.name, cards_count])
		print("    总宽度: %d像素" % total_width)
		print("    是否适合: %s" % ("✅" if fits else "❌"))
		if overflow > 0:
			print("    溢出: %d像素" % overflow)
		print("")

# 创建一个简单的可视化测试
func create_visual_test():
	print("\n🎨 创建可视化测试...")
	
	# 这个函数可以在实际场景中调用来测试布局
	var test_game = PokerGame.new()
	test_game.start_new_game()
	
	print("  游戏已开始，可以观察卡牌布局")
	print("  玩家手牌: %d张" % test_game.get_player_hand().size())
	print("  庄家手牌: %d张" % test_game.get_dealer_hand().size())
	
	# 模拟玩家要更多牌
	for i in range(3):
		if test_game.can_player_hit():
			test_game.player_hit()
			print("  玩家要牌后: %d张" % test_game.get_player_hand().size())
