extends Node

# 测试扑克牌布局和尺寸优化

func _ready():
	print("🃏 测试扑克牌布局优化")
	print("=" * 40)
	
	test_card_dimensions()
	test_layout_calculations()
	test_multiple_cards_scenario()
	
	print("=" * 40)
	print("✅ 布局测试完成！")

func test_card_dimensions():
	print("\n📏 测试扑克牌尺寸：")
	
	# 底栏总高度
	var bottom_bar_height = 50
	print("  底栏高度: %d像素" % bottom_bar_height)
	
	# 优化后的扑克牌尺寸
	var card_width = 16
	var card_height = 22
	var card_ratio = float(card_width) / float(card_height)
	
	print("  扑克牌尺寸: %dx%d像素" % [card_width, card_height])
	print("  宽高比: %.2f" % card_ratio)
	print("  高度占底栏比例: %.1f%%" % (float(card_height) / float(bottom_bar_height) * 100))
	
	# 检查是否合理
	if card_height <= bottom_bar_height * 0.8:
		print("  ✅ 扑克牌高度合适，留有足够边距")
	else:
		print("  ⚠️ 扑克牌可能过高")

func test_layout_calculations():
	print("\n📐 测试布局计算：")
	
	# 假设底栏宽度（常见屏幕宽度）
	var screen_widths = [1920, 1366, 1024]
	
	for width in screen_widths:
		print("  屏幕宽度: %d像素" % width)
		
		# 三个区域平分宽度
		var area_width = width / 3
		print("    每个区域宽度: %d像素" % area_width)
		
		# 计算每个区域最多能放多少张牌
		var card_width = 16
		var card_spacing = 1
		var area_padding = 10  # 区域内边距
		
		var available_width = area_width - area_padding * 2
		var max_cards = (available_width + card_spacing) / (card_width + card_spacing)
		
		print("    最多可放扑克牌: %d张" % max_cards)
		
		if max_cards >= 8:
			print("    ✅ 足够显示常见手牌数量")
		elif max_cards >= 5:
			print("    ⚠️ 可显示基本手牌，但空间紧张")
		else:
			print("    ❌ 空间不足，需要进一步优化")

func test_multiple_cards_scenario():
	print("\n🃏 测试多张牌场景：")
	
	# 模拟不同的手牌数量
	var scenarios = [
		{"name": "初始发牌", "cards": 2},
		{"name": "正常游戏", "cards": 4},
		{"name": "多次要牌", "cards": 6},
		{"name": "极端情况", "cards": 8}
	]
	
	var card_width = 16
	var card_spacing = 1
	var area_width = 640  # 1920/3的近似值
	var area_padding = 10
	
	for scenario in scenarios:
		var card_count = scenario.cards
		var total_width = card_count * card_width + (card_count - 1) * card_spacing + area_padding * 2
		
		print("  %s (%d张牌):" % [scenario.name, card_count])
		print("    需要宽度: %d像素" % total_width)
		print("    可用宽度: %d像素" % area_width)
		
		if total_width <= area_width:
			print("    ✅ 布局正常")
		else:
			print("    ❌ 可能溢出，建议:")
			# 计算建议的重叠显示
			var overlap_needed = total_width - area_width
			var overlap_per_card = overlap_needed / (card_count - 1)
			print("      - 使用重叠显示，每张牌重叠 %.1f像素" % overlap_per_card)
		print("")
