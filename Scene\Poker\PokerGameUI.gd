class_name PokerGameUI
extends Control

# UI组件引用
@onready var player_hand_container: HBoxContainer = $MainContainer/PlayerArea/PlayerHandContainer
@onready var dealer_hand_container: HBoxContainer = $MainContainer/DealerArea/DealerHandContainer
@onready var player_score_label: Label = $MainContainer/PlayerArea/PlayerScoreLabel
@onready var dealer_score_label: Label = $MainContainer/DealerArea/DealerScoreLabel
@onready var game_status_label: Label = $MainContainer/CenterArea/GameStatusLabel
@onready var button_container: HBoxContainer = $MainContainer/CenterArea/ButtonContainer
@onready var hit_button: Button = $MainContainer/CenterArea/ButtonContainer/HitButton
@onready var stand_button: Button = $MainContainer/CenterArea/ButtonContainer/StandButton
@onready var new_game_button: Button = $MainContainer/CenterArea/ButtonContainer/NewGameButton

# 游戏逻辑
var poker_game: PokerGame
var player_card_uis: Array[PokerCardUI] = []
var dealer_card_uis: Array[PokerCardUI] = []

# 预加载扑克牌UI场景
var poker_card_ui_scene = preload("res://Scene/Poker/PokerCardUI.tscn")

func _ready():
    # 初始化游戏
    poker_game = PokerGame.new()
    
    # 连接游戏信号
    poker_game.game_started.connect(_on_game_started)
    poker_game.game_ended.connect(_on_game_ended)
    poker_game.card_dealt.connect(_on_card_dealt)
    poker_game.turn_changed.connect(_on_turn_changed)
    
    # 连接按钮信号
    hit_button.pressed.connect(_on_hit_button_pressed)
    stand_button.pressed.connect(_on_stand_button_pressed)
    new_game_button.pressed.connect(_on_new_game_button_pressed)
    
    # 初始化UI状态
    update_ui_state()
    game_status_label.text = "点击新游戏开始"

func _on_game_started():
    game_status_label.text = "游戏开始！"
    clear_hands()
    update_ui_state()

func _on_game_ended(winner: String, player_score: int, dealer_score: int):
    var status_text = ""
    if winner == "平局":
        status_text = "平局！玩家: %d, 庄家: %d" % [player_score, dealer_score]
    else:
        status_text = "%s获胜！玩家: %d, 庄家: %d" % [winner, player_score, dealer_score]
    
    game_status_label.text = status_text
    update_ui_state()

func _on_card_dealt(card: PokerCard, is_player: bool):
    var card_ui = poker_card_ui_scene.instantiate() as PokerCardUI
    card_ui.set_poker_card(card)
    card_ui.set_card_size(Vector2(10, 14))  # 很小的尺寸，适合50px底栏
    
    if is_player:
        player_hand_container.add_child(card_ui)
        player_card_uis.append(card_ui)
        # 播放发牌动画
        var from_pos = Vector2(size.x / 2, size.y / 2)
        card_ui.play_deal_animation(from_pos, player_card_uis.size() * 0.2)
    else:
        dealer_hand_container.add_child(card_ui)
        dealer_card_uis.append(card_ui)
        # 播放发牌动画
        var from_pos = Vector2(size.x / 2, size.y / 2)
        card_ui.play_deal_animation(from_pos, dealer_card_uis.size() * 0.2)
    
    # 延迟更新分数，等待动画完成
    await get_tree().create_timer(0.3).timeout
    update_scores()

func _on_turn_changed(is_player_turn: bool):
    if is_player_turn:
        game_status_label.text = "你的回合"
    else:
        game_status_label.text = "庄家回合"
        # 翻开庄家的背面牌
        for i in range(dealer_card_uis.size()):
            var card_ui = dealer_card_uis[i]
            var card = poker_game.get_dealer_hand()[i]
            if not card.is_face_up:
                card_ui.flip_card()
    
    update_ui_state()

func _on_hit_button_pressed():
    poker_game.player_hit()

func _on_stand_button_pressed():
    poker_game.player_stand()
    # 处理庄家的延迟发牌动画
    _handle_dealer_turn()

func _on_new_game_button_pressed():
    poker_game.start_new_game()

# 更新UI状态
func update_ui_state():
    var state = poker_game.get_current_state()
    
    hit_button.disabled = not poker_game.can_player_hit()
    stand_button.disabled = not poker_game.can_player_stand()
    new_game_button.disabled = (state == PokerGame.GameState.DEALING)
    
    update_scores()

# 更新分数显示
func update_scores():
    player_score_label.text = "玩家: %d" % poker_game.get_player_score()
    
    var dealer_score = poker_game.get_dealer_visible_score()
    if poker_game.get_current_state() == PokerGame.GameState.GAME_OVER:
        dealer_score = poker_game.calculate_hand_value(poker_game.get_dealer_hand())
    
    dealer_score_label.text = "庄家: %d" % dealer_score

# 清空手牌显示
func clear_hands():
    # 清空玩家手牌
    for card_ui in player_card_uis:
        if is_instance_valid(card_ui):
            card_ui.queue_free()
    player_card_uis.clear()
    
    # 清空庄家手牌
    for card_ui in dealer_card_uis:
        if is_instance_valid(card_ui):
            card_ui.queue_free()
    dealer_card_uis.clear()

# 设置游戏区域大小
func set_game_area_size(new_size: Vector2):
    size = new_size
    custom_minimum_size = new_size

# 处理庄家回合的延迟发牌
func _handle_dealer_turn():
    if poker_game.get_current_state() != PokerGame.GameState.DEALER_TURN:
        return

    # 添加延迟让玩家看到庄家的发牌过程
    await get_tree().create_timer(1.0).timeout
    update_scores()
