extends Node

# 扑克游戏演示脚本
# 用于展示扑克游戏的各种功能

func _ready():
	print("=== 扑克游戏演示 ===")
	print("底栏扑克游戏已成功集成！")
	print("")
	
	demo_poker_cards()
	demo_game_logic()
	print("")
	print("演示完成！现在可以在底栏中体验完整的21点扑克游戏。")

func demo_poker_cards():
	print("📱 扑克牌演示：")
	
	# 演示不同花色和牌面
	var suits = [PokerCard.Suit.HEARTS, PokerCard.Suit.SPADES, PokerCard.Suit.DIAMONDS, PokerCard.Suit.CLUBS]
	var ranks = [PokerCard.Rank.ACE, PokerCard.Rank.KING, PokerCard.Rank.QUEEN, PokerCard.Rank.JACK]
	
	for i in range(4):
		var card = PokerCard.new(suits[i], ranks[i])
		print("  %s - 纹理路径: %s" % [card.get_display_name(), card.get_texture_path()])
	
	print("  牌背纹理: %s" % PokerCard.new().get_back_texture_path())
	print("")

func demo_game_logic():
	print("🎮 游戏逻辑演示：")
	
	var game = PokerGame.new()
	print("  初始状态: %s" % game.get_current_state())
	
	# 连接信号进行演示
	game.game_started.connect(_on_demo_game_started)
	game.card_dealt.connect(_on_demo_card_dealt)
	game.game_ended.connect(_on_demo_game_ended)
	
	print("  开始新游戏...")
	game.start_new_game()
	
	print("  玩家手牌: %d张" % game.get_player_hand().size())
	print("  庄家手牌: %d张" % game.get_dealer_hand().size())
	print("  玩家分数: %d" % game.get_player_score())
	print("  庄家可见分数: %d" % game.get_dealer_visible_score())

func _on_demo_game_started():
	print("  ✅ 游戏开始信号触发")

func _on_demo_card_dealt(card: PokerCard, is_player: bool):
	var player_type = "玩家" if is_player else "庄家"
	var face_status = "正面" if card.is_face_up else "背面"
	print("  🃏 发牌: %s 获得 %s (%s)" % [player_type, card.get_display_name(), face_status])

func _on_demo_game_ended(winner: String, player_score: int, dealer_score: int):
	print("  🏆 游戏结束: %s获胜 (玩家:%d vs 庄家:%d)" % [winner, player_score, dealer_score])
