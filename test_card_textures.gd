extends Node

# 测试扑克牌纹理加载

func _ready():
	print("=== 测试扑克牌纹理加载 ===")
	test_card_textures()

func test_card_textures():
	# 测试几张代表性的牌
	var test_cards = [
		[PokerCard.Suit.HEARTS, PokerCard.Rank.ACE],
		[PokerCard.Suit.SPADES, PokerCard.Rank.KING],
		[PokerCard.Suit.DIAMONDS, PokerCard.Rank.QUEEN],
		[PokerCard.Suit.CLUBS, PokerCard.Rank.JACK],
		[PokerCard.Suit.HEARTS, PokerCard.Rank.TEN]
	]
	
	for card_data in test_cards:
		var card = PokerCard.new(card_data[0], card_data[1])
		var texture_path = card.get_texture_path()
		
		print("测试卡牌: %s" % card.get_display_name())
		print("  纹理路径: %s" % texture_path)
		
		# 尝试加载纹理
		if ResourceLoader.exists(texture_path):
			var texture = load(texture_path)
			if texture:
				print("  ✅ 纹理加载成功")
			else:
				print("  ❌ 纹理加载失败")
		else:
			print("  ❌ 纹理文件不存在")
		print("")
	
	# 测试牌背
	var back_path = PokerCard.new().get_back_texture_path()
	print("测试牌背:")
	print("  纹理路径: %s" % back_path)
	if ResourceLoader.exists(back_path):
		print("  ✅ 牌背纹理存在")
	else:
		print("  ❌ 牌背纹理不存在")
	
	print("\n纹理测试完成！")
